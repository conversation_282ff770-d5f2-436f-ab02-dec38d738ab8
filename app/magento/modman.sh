
function modmanCreate() {
    local APPCONTAINER=$(getContainerNameByType "$APP")
    if [ $APPCONTAINER ]; then
        local MODULNAME=$@
        if [ -f "htdocs/meff.php" ]; then
            ($DOCKER exec -ti $APPCONTAINER php meff.php $MODULNAME | grep . | grep / | sed 's/\/var\/www\/htdocs\///g') > modman
            if [[ (-s modman) && (! $(head -c 3 modman) = PHP ) ]] ; then
                while read p; do
                  local FILENAME=$(echo $p | sed 's/\r$//')
                  if ! cp --parents -r htdocs/$FILENAME .modman/ ; then
                    echo "Unable to copy $FILENAME from module $MODULNAME"
                  fi
                done <modman
                mv .modman/htdocs .modman/$MODULNAME
                mv modman .modman/$MODULNAME/modman
            else
                rm modman
                __err "Unable to detect module files for $MODULNAME"
            fi
        else
            __err "Please install meff (https://github.com/tegansnyder/meff)"
        fi
        return 0
    else
        __err "Please start the app container"
        return -1
    fi
}

function modmanCreateSingle(){
    if [ $MAGENTO_VERSION == 2 ]; then
       __err "Magento 2 not supported"
    else
       modmanCreate $parameters
    fi
}

function modmanCreateAll(){
    if [ $MAGENTO_VERSION == 2 ]; then
        __err "Magento 2 not supported"
    else
        local communityModules=$( executeMagerun sys:modules:list --codepool="community" --format csv | grep ,active | sed 's/community,//g' | cut -d',' -f1)
        local localModules=$( executeMagerun sys:modules:list --codepool="local" --format csv | grep ,active | sed 's/local,//g' | cut -d',' -f1)
        for f in $communityModules $localModules
        do
            modmanCreate $f
        done
    fi
}