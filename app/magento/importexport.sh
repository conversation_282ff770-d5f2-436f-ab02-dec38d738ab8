function executeDBImport() {
    if [ ! "$@" ]; then
        files=(db/import/*.sql.gz)
        select SEL in "${files[@]}" "Skip"
        do
            if [ ! "$SEL" ]; then
                __msg "Choose one of the available files."
                continue
            fi
            if [ "$SEL" = "Skip" ]; then
                __msg "Skipping DB Import"
                break
            fi
            while true; do
                read -p "Do you want to drop the old database? (y/n - default no)" yn
                case $yn in
                    [Yy]* ) executeMagerun db:drop && executeMagerun db:create; break;;
                    * ) break;;
                esac
            done
            __info "$SEL will be imported"
	          if [ $MAGENTO_VERSION == 1 ]; then
                executeMagerun db:import -c gzip ../"$SEL"
            else
                executeMagerun db:import -c gzip ./"$SEL"
            fi
            # mysql -u magento -p magento -h mysql < mysql.sql
            break
        done
    else
        __info "$@ will be imported"
        executeMagerun db:import -c gzip "$@"
    fi
    importDbAfter
}

function executeDBExport () {
    if [ ! -d $rootDir/db/export ]; then
      mkdir -p $rootDir/db/export
    fi
    FILE=../db/export/dump.sql.gz
    if [ $MAGENTO_VERSION == 2 ]; then
        FILE=db/export/dump.sql.gz
    fi
    executeMagerun db:dump -c gzip $FILE
}

function executeDownloadDb () {
    if [ ! -d $rootDir/db/import ]; then
      mkdir -p $rootDir/db/import
    fi
    local params=$@
    if [ ! "$@" ]; then
        params="production"
    fi
    if [ -f $rootDir/config/deploy.php ]; then
      $DOCKER run --rm -it \
        --workdir "$rootDir" \
        -v "$rootDir":"$rootDir" \
        -v ~/.ssh/known_hosts:/root/.ssh/known_hosts:ro \
        -v "$(realpath ~/.ssh/config)":/root/.ssh/config_orig:ro \
        -v $SSH_AUTH_SOCK:$SSH_AUTH_SOCK \
        -e SSH_AUTH_SOCK=$SSH_AUTH_SOCK \
		    --entrypoint /bin/bash \
        deployphp/deployer:v7.5.6 -c "
         cp /root/.ssh/config_orig /root/.ssh/config && \
         chmod +x /bin/deployer.phar && \
         /bin/deployer.phar --file=$rootDir/config/deploy.php run '[ -f {{current_path}}/bin/n98-magerun2 ] || [ -f {{current_path}}/vendor/bin/n98-magerun2 ] || (curl -o {{current_path}}/bin/n98-magerun2 https://files.magerun.net/n98-magerun2.phar && chmod +x {{current_path}}/bin/n98-magerun2)' $params && \
         /bin/deployer.phar --file=$rootDir/config/deploy.php db:download $params
       "
      return $? # Return the exit status of the previous command
    else
      __info "No deployer found. Please consider upgrading your project."
      if command -v cap >/dev/null 2>&1 && cap -V >/dev/null 2>&1; then
        cap $params db:download
        return $? # Return the exit status of the previous command
      else
        __err "Deployer and Capistrano not installed or not working correctly"
        return 1
      fi
    fi
}

function executeMediaDownload() {
    local params=$@
    if [ ! "$@" ]; then
        params="production"
    fi
    cap $params media:download
}

function importDbAfter() {

    if [ $MAGENTO_VERSION = 2 ]; then
        while true; do
            read -p "Do you want to run setup upgrade (y/n - default yes)" yn
            case $yn in
                [Nn]* ) break;;
                * )
                  __msg "Running setup script"
                  executeMagerun setup:upgrade
                  break;;
            esac
        done

        while true; do
            read -p "Do you want to set indexer mode to realtime (y/n - default yes)" yn
            case $yn in
                [Nn]* ) break;;
                * ) executeMagento2 indexer:set-mode realtime; break;;
            esac
        done
    fi

    setBaseUrl

    resetAdminPassword

    if [ $MAGENTO_VERSION == 1 ]; then
        __msg "Running setup script"
        executeMagerun sys:setup:run
        __msg "Enable logging"
        setConfig dev/log/active 1
        __msg "Enable symbolic links for store front"
        setConfig dev/template/allow_symlink 1
        __msg "Remove cookie Domain"
        executeMagerun config:delete web/cookie/cookie_domain

        installPhpUnitDB
    else
        setConfig admin/security/session_lifetime 31536000
        setConfig system/security/max_session_size_admin 2560000
    fi

    if [ -f config/magento/db/import.sql ]; then
      executeMagerun db:query "$(cat config/magento/db/import.sql)"
    fi
}