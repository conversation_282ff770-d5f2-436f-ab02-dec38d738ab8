#!/usr/bin/env bash
[[ ! ${SCRIPT_DIR} ]] && >&2 __err "This script is not intended to be run directly!" && exit 1

MAGENTO_VERSION=1

parseDockerCompose
if [[ $rootDir ]]; then
  cd ${rootDir} # Change into root dir
  if [ "${rootDir}/composer.json" ] && [ -f "${rootDir}/composer.json"  ]; then
      if grep -q  magento/product-community "${rootDir}/composer.json"; then
         MAGENTO_VERSION=2
         MAGENTO_EXACT_VERSION=$(grep magento/product-community "${rootDir}/composer.json" | awk -F'"' '{print $4}')
      fi
  fi
else
  rootDir=$(pwd)
fi