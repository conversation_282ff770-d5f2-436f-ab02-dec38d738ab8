
function magepackGenerate() {
    local cms=$docker_compose_app_environment_MAGEPACK_CMS_URL
    local category="$docker_compose_app_environment_MAGEPACK_CATEGORY_PAGE_URL"
    local product="$docker_compose_app_environment_MAGEPACK_PRODUCT_PAGE_URL"
    local containerName=$(getContainerNameByType $APP)
    if [ "$($DOCKER exec ${containerName} which magepack)" > /dev/null ]; then
      if grep -q  "creativestyle/magesuite-magepack" "${rootDir}/composer.json"; then
        PARAMS=(${parameters// / })
        if [ "${PARAMS[0]}" ]; then
          category="${PARAMS[0]}"
        fi
        if [ "${PARAMS[1]}" ]; then
          product="${PARAMS[1]}"
        fi
        if [ -z $cms ]; then
          __err "Please enter cms-page url"
          while true; do
            read -p "CMS-Page Url:" cms
            if [ "$cms" ]; then
              break;
            fi
          done
        fi
        if [ -z $category ]; then
          __err "Please enter category url"
          while true; do
            read -p "Category Url:" category
            if [ "$category" ]; then
              break;
            fi
        done
        fi
        if [ -z $product ]; then
          __err "Please enter product url"
          while true; do
            read -p "Product Url:" product
            if [ "$product" ]; then
              break;
            fi
          done
        fi
        __msg "Start creating magepack config"
        __info "Running  magepack generate --cms-url=\"${cms}\" --category-url=\"${category}\" --product-url=\"${product}\""
        $DOCKER exec -it -u $DOCKER_USER ${containerName} magepack generate --cms-url="${cms}" --category-url="${category}" --product-url="${product}"
      else
        __err "please install magepack Magento module"
        __info "composer require creativestyle/magesuite-magepack"
      fi
    else
        __err "magepack not detected"
    fi
}

function magepackBundle() {
    local containerName=$(getContainerNameByType $APP)
    if [ "$($DOCKER exec ${containerName} which magepack)" > /dev/null ]; then
      if grep -q  "creativestyle/magesuite-magepack" "${rootDir}/composer.json"; then
        if [ -f ${rootDir}/magepack.config.js ]; then
          __info "Running magepack bundle"
          $DOCKER exec -it -u $DOCKER_USER ${containerName} magepack bundle $parameters
        else
          __err "Did you run magepack:generate? Couldn't find magepack.config.js"
          fi
      else
        __err "Please install magepack Magento module"
        __info "composer require creativestyle/magesuite-magepack"
      fi
    else
        __err "Magepack not detected"
    fi
}
