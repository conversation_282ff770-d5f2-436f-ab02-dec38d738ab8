source "$SCRIPT_DIR/app/magento/importexport.sh"

function downloadN98(){
    if [[ ! -d "${rootDir}/bin" ]]; then
        mkdir -p "${rootDir}/bin"
    fi
    cd "${rootDir}/bin"
    local version="n98-magerun"
    if [ $MAGENTO_VERSION == 2 ]; then
        version="n98-magerun2"
    fi
    if [[ ! -e ${version} ]]; then
        __attn "Downloading ${version}" >&2
        curl -O https://files.magerun.net/${version}.phar
        mv ${version}.phar ${version}
        chmod +x ${version}
    fi
}

function executeMagerun() {
    MAGERUN=../bin/n98-magerun
    if [ $MAGENTO_VERSION == 2 ]; then
        MAGERUN=bin/n98-magerun2
    fi
    if [[ -f "${rootDir}/vendor/bin/n98-magerun2" ]]; then
     MAGERUN=vendor/bin/n98-magerun2
    else
      downloadN98
    fi

    local APPCONTAINER=$(getContainerNameByType "$APP")
    $DOCKER exec -u www-data -ti $APPCONTAINER $MAGERUN "$@"
    return 0
}

function executeMagento2() {
    if [ $MAGENTO_VERSION == 2 ]; then
        local params=$@
        if [ ! "$params" ]; then
            params="$parameters"
        fi
        local CONTAINER=$(getContainerNameByType "$APP")
        $DOCKER exec -u www-data -ti $CONTAINER chmod +x "./bin/magento"
        $DOCKER exec -u www-data -ti $CONTAINER ./bin/magento $params
    else
        __err "This is not a Magento2 instance!"
    fi
}

function rmgenerated () {
     if [ $MAGENTO_VERSION == 2 ]; then
        local containerName=$(getContainerNameByType $APP)
        local toRemove="var/generation/* var/cache/* var/tmp/* var/view_preprocessed/* var/page_cache/* generated/* generation/* pub/static/*"
        local command="rm -rf $toRemove"
        $DOCKER exec -t $containerName bash -c "$command"
        __attn "Cleaned: $toRemove"
    else
        __err "M2 not detected"
    fi
}

function watchCache() {
    if [ $MAGENTO_VERSION == 2 ]; then
        local containerName=$(getContainerNameByType $APP)
        local EXISTS=$($DOCKER exec -it $containerName bash -c "test -e vendor/bin/cache-clean.js && echo 'true'")
        if [[ "${EXISTS//[$'\t\r\n ']}" == "true" ]]; then
            __attn "Watching Cache Now!"
            $DOCKER exec -it $containerName bash -c "vendor/bin/cache-clean.js --watch"
        else
            __err "CacheClean not detected: composer require --dev mage2tv/magento-cache-clean"
        fi
    else
        __err "M2 not detected"
    fi
}

function watchStatic() {
    local proxy=$docker_compose_app_environment_DOMAIN
    if [ "$parameters" ]; then
        proxy=$parameters
    fi
    local containerName=$(getContainerNameByType $APP)
    local files="../.modman/**/*.css, ../.modman/**/*.js, skin/frontend/**/*.css, skin/frontend/**/*.js"
     if [ $MAGENTO_VERSION == 2 ]; then
        files="app/design/frontend/**/*.css, app/design/frontend/**/*.js, pub/static/frontend/**/*.css, pub/static/frontend/**/*.js"
    fi
    echo "Watching $files in $containerName"
    if [ "$($DOCKER exec ${containerName} which browser-sync)"  > /dev/null ]; then
        $DOCKER exec -it ${containerName} \
            browser-sync start \
            --proxy "$proxy" \
            --files "${files}" \
            --no-open
    else
        __err "browser-sync not detected"
    fi
}

function watchStaticLivereload() {
    local containerName=$(getContainerNameByType $APP)
    if [ "$($DOCKER exec ${containerName} which grunt)" > /dev/null ]; then
      if grep -q  "magento2-livereload" "${rootDir}/composer.json"; then
        local exec="no"
        while true; do
            read -p "Do you want to build theme before watching? (y/n)" yn
            case $yn in
                [Yy]* ) exec="yes"; break;;
                [Nn]* ) break;;
                * ) echo "Please answer [y]es or [n]o.";;
            esac
        done
        __msg "Starting Live Reload"
        if [ -f Gruntfile.js.sample ]; then
          mv Gruntfile.js.sample Gruntfile.js
        fi
        if [ -f package.json.sample ]; then
          mv package.json.sample package.json
        fi
        $DOCKER exec -it ${containerName} npm install
        if [ "$exec" == "yes" ]; then
          $DOCKER exec -it -u $DOCKER_USER ${containerName} grunt exec
          $DOCKER exec -it -u $DOCKER_USER ${containerName} grunt less -f
        fi
        $DOCKER exec -it ${containerName} grunt watch
      else
        __err "please install livereload"
        __info "composer require tdpsoft/magento2-livereload"
      fi
    else
        __err "livereload not detected"
    fi
}

function importdb() {
    executeDBImport $parameters
}

function exportdb(){
    executeDBExport
}

function downloaddb(){
    executeDownloadDb $parameters || return 1
}

function downloadmedia(){
    executeMediaDownload $parameters
}

function audit () {
    executeAudit
}

function install() {
  if df -T . | grep -q "ecryptfs"; then
      read -p "The current directory is encrypted and you cant install Magento with MSI here. Do you want to continue? [Y/n] " choice

      # Check the user's choice
      case "$choice" in
          "Y" | "y" )
              installMagento $parameters
              ;;
          * )
              echo "Please don't install Magento on an encrypted filesystem if MSI is enabled."
              exit 0
              ;;
      esac
  else
    installMagento $parameters
  fi
}

function magerun() {
    executeMagerun $parameters
}

function cleancache(){
#    if [ -z "$parameters" -a -n "$docker_compose_redis_image" ]; then
#      __info "Using redis clean"
#      cleanredis
#    else
      executeMagerun cache:clean $parameters
#    fi
}

function flushcache(){
    executeMagerun cache:flush
}

function installPhpUnitDB() {
    #Install PhpUnit
    __info "Creating phpUnit test database"
    $DOCKER exec -ti $(getContainerNameByType "$MYSQL") mysqladmin -uroot create magento_unit_tests -pr00t

    if [ -e htdocs/shell/ecomdev-phpunit-install.php ]; then
		__info "Creating phpUnit test tables"
		$DOCKER exec -ti $(getContainerNameByType "$APP") php shell/ecomdev-phpunit-install.php
    fi
}

function checkImageDate(){
    local imageName="copex/nginx-php-fpm:dev"
    local image_date=$(getContainerDate $imageName)
    if [ $image_date -lt $LATEST_IMAGE ];
    then
        while true; do
            read -p "Do you want to update $imageName? (y/n)" yn
            case $yn in
                [Yy]* ) __info "Updating image $imageName"; $DOCKER pull "$imageName"; break;;
                [Nn]* ) break;;
                * ) echo "Please answer [y]es or [n]o.";;
            esac
        done

    fi
    checkMailcatcher
}

function checkMailcatcher() {
  if [ "$docker_compose_app_image" = "copex/nginx-php-fpm:dev" ]; then
    devContainer=$(getContainerDate "copex/nginx-php-fpm:dev")
    if [ "$devContainer" -ge 1717153199 ]; then
      if [ -z $docker_compose_mailcatcher_image ]; then
        local docker_compose_override_file="$docker_compose_path/docker-compose.local.yml"
        if [[ -f "$docker_compose_path/docker-compose.local.yml" ]]; then
            sed -i '/- "1080:1080"/d' "$docker_compose_path/docker-compose.local.yml"
        fi
        if [[ -f "$docker_compose_path/docker-compose.override.yml" ]]; then
            sed -i '/- "1080:1080"/d' "$docker_compose_path/docker-compose.override.yml"
            docker_compose_override_file="$docker_compose_path/docker-compose.override.yml"
        fi
        echo '  mailcatcher:
    image: sj26/mailcatcher
    ports:
      - "1080:1080"
    networks:
      - backend' >> "${docker_compose_override_file}"
        __info "Mailcatcher was added to file ${docker_compose_override_file} - see https://kb.copex.io/books/development/page/setup#bkmrk-mailcatcher"
      fi
    fi
  fi
}

function setBaseUrl() {
    local PROTOCOL="http"
    if [ "$@" ]; then
        DOMAIN="$@"
    elif [ "$parameters" ] && [[ "$parameters" == *.* ]]; then
        DOMAIN="$parameters"
    elif [ "$docker_compose_app_environment_DOMAIN" ]; then
        DOMAIN="$docker_compose_app_environment_DOMAIN"
    else
        local APPCONTAINER=$(getContainerNameByType "$APP")
        DOMAIN=$($DOCKER exec -ti $APPCONTAINER bash -c 'echo "$DOMAIN"|tr -d "\n"')
    fi
    if [ "$docker_compose_app_environment_SSL_ON" == 1 ]; then
        PROTOCOL="https"
    fi
    __attn "Setting base url to ${PROTOCOL}://$DOMAIN"
    executeMagerun db:query "update core_config_data set value='${PROTOCOL}://${DOMAIN}/' where path like 'web%secure%base_url'" > /dev/null
    executeMagerun db:query "update core_config_data set value='${PROTOCOL}://${DOMAIN}/' where path like 'web%secure%base_link_url'" > /dev/null
    executeMagerun db:query "delete FROM core_config_data where path like 'web%secure%base%' and scope_id <> 0" > /dev/null
    executeMagerun db:query "delete FROM core_config_data where path like 'web/cookie/cookie_domain' and scope_id <> 0" > /dev/null
    executeMagerun db:query "delete FROM core_config_data where path like 'dev/%/merge%files' and scope = 'stores'" > /dev/null
    executeMagerun db:query "update core_config_data set value = null where path like 'web/%secure/base_static_url'" > /dev/null
    executeMagerun db:query "update core_config_data set value = null where path like 'web/%secure/base_media_url'" > /dev/null
    executeMagerun db:query "update core_config_data set value = null where path = 'web/cookie/cookie_domain'" > /dev/null
    __info "Cleaning Config"
    executeMagerun cache:clean config
}

function installMagento() {

    local COMPOSER_VERSION=$(executeComposer --version | tail -n 1 | sed -r "s/[[:cntrl:]]\[[0-9]{1,3}m//g" | awk '{print substr($3,1,1);}')
    if [ $COMPOSER_VERSION == 1 ]; then
      local tmpDockerUser=$DOCKER_USER
      DOCKER_USER=root
      executeComposer global require hirak/prestissimo
      DOCKER_USER=$tmpDockerUser
    fi

    executeComposer install

    symlinkConfigFiles

    if [ $MAGENTO_VERSION == 2 ]; then
         if [ ! -e app/etc/env.php ]; then
            local sampleData="no"
            while true; do
                read -p "Do you want to install sample data? (y/n)" yn
                case $yn in
                    [Yy]* ) sampleData="yes"; break;;
                    [Nn]* ) break;;
                    * ) echo "Please answer [y]es or [n]o.";;
                esac
            done

            executeMagerun install --dbHost="$MYSQL" \
             --dbUser="$docker_compose_mysql_environment_MYSQL_USER" \
             --dbPass="$docker_compose_mysql_environment_MYSQL_PASSWORD" \
             --dbName="$docker_compose_mysql_environment_MYSQL_DATABASE" \
             --installSampleData=$sampleData \
             --useDefaultConfigParams=yes \
             --magentoVersion="$docker_compose_app_environment_MAGENTO_VERSION" \
             --baseUrl="$docker_compose_app_environment_DOMAIN" \
             --noDownload
        fi
    fi

    while true; do
        read -p "Do you want to try to download a database from the live system? (y/n - default no)" yn
        case $yn in
            [Yy]* )
                if ! executeDownloadDb; then
                    echo "Skipping database download as it failed or dependencies are not available."
                fi
                break;;
            * ) break;;
        esac
    done

    executeDBImport
    fixPermissions
    if [ -f "${rootDir}/config/index.php" ]; then
      __msg "Replacing index.php in pub folder"
      cp -f "${rootDir}/config/index.php" "${rootDir}/index.php"
    fi
}

function setConfig() {
    local setCommand="config:set"
    if [ $MAGENTO_VERSION == 2 ]; then
        if [  "$MAGENTO_EXACT_VERSION" = "$(echo -e "$MAGENTO_EXACT_VERSION\n2.2" | sort -V | head -n1)" ]; then
            setCommand="config:store:set"
        fi
    fi
    set +e
    executeMagerun $setCommand $@
    set -e
}

function symlinkConfigFiles() {
    if [ $MAGENTO_VERSION == 1 ]; then
        symlinkMagento1ConfigFiles
    else
        symlinkMagento2ConfigFiles
    fi
}

function symlinkMagento1ConfigFiles(){
 # Symlink local.local.xml or local.dev.xml to local.xml in config folder
    if [ ! -e config/local.xml ]; then
        if [ -e config/local.local.xml ]; then
            ln -s local.local.xml config/local.xml
        else
            if [ -e config/local.dev.xml ]; then
                ln -s local.dev.xml config/local.xml
            fi
        fi
    fi

    if [ -e config/local.xml ]; then #symlink local.xml from config
        __msg "Symlinking local.xml"
        chmod +r config/local.xml
        ln -fs ../../../config/local.xml htdocs/app/etc/local.xml
    else
        #Create DB Config (n98)
        __msg "Creating local.xml"
        executeMagerun local-config:generate mysql magento 'magento' magento db backoffice
        cp app/etc/local.xml ../config/local.xml
    fi
}

function symlinkMagento2ConfigFiles() {
    # Symlink env.php
    if [ ! -f config/etc/env.php ]; then
        if [ -e config/etc/env.local.php ]; then
            ln -s env.local.php config/etc/env.php
        fi
    fi

    if [ -f config/etc/env.php ]; then #symlink env.php from config
        __msg "Symlinking env.php"
        chmod +r config/etc/env.php
        ln -fs ../../config/etc/env.php app/etc/env.php
    else
        #Copy env.php back to config folder
        cp app/etc/env.php config/etc/env.local.php
        ln -s env.local.php config/etc/env.php
    fi
}

function resetAdminPassword() {
    __msg "Setting admin password"
    local adminExists=$(executeMagerun admin:user:list | grep " admin ")
    if [ "$adminExists" ]; then
        executeMagerun admin:user:change-password admin admin123
    else
        __msg "Creating admin user"
        if [ $MAGENTO_VERSION == 2 ]; then
            executeMagerun admin:user:create --admin-user=admin --admin-email=<EMAIL> --admin-password=admin123 --admin-firstname=Admini --admin-lastname=Strator
        else
            executeMagerun admin:user:<NAME_EMAIL> admin123 Admini Strator
        fi
    fi
}

function fixPermissions() {
    local APPCONTAINER=$(getContainerNameByType "$APP")

    if [ $MAGENTO_VERSION == 1 ]; then
        sudo chown -R $USER:$USER .*
        sudo chown -R $USER:$USER *
        $DOCKER exec -ti $APPCONTAINER chgrp $DOCKER_USER -Rf var sitemaps media
        $DOCKER exec -ti $APPCONTAINER chmod 775 -Rf var sitemaps media
        $DOCKER exec -ti $APPCONTAINER chmod +x bin/n98-magerun
    else
      $DOCKER exec -ti $APPCONTAINER mkdir -p var/cache generated
      $DOCKER exec -ti $APPCONTAINER chgrp $DOCKER_USER -Rf pub/media pub/static var generated
      $DOCKER exec -ti $APPCONTAINER chmod 775 -Rf pub/media pub/static var generated
      $DOCKER exec -ti $APPCONTAINER chown -R $DOCKER_USER:www-data /var/www/htdocs
      $DOCKER exec -ti $APPCONTAINER chmod +x bin/magento
      $DOCKER exec -ti $APPCONTAINER chmod +x bin/n98-magerun2
    fi
}

function fixPermissionsAll(){
    # sudo find ${rootDir} \! -user $(id -u -n) -exec chown $(id -u -n):$DOCKER_USER {} \;
    sudo find ${rootDir}  -type d -exec chmod 755 {} \;
    sudo find ${rootDir}  -type f -exec chmod 644 {} \;
}

function executeAudit() {
    if [ $MAGENTO_VERSION == 2 ]; then
        __msg "Audit is currently only supported for Magento v1"
    else
        if [ "$parameters" ]; then

            __msg "Generating audit info"
            $DOCKER run --rm \
            --link=$(getContainerNameByType "$MYSQL"):mysql \
            -v $(pwd):/project \
            -v $(pwd)/magento_src:/magento_src \
            -v $(pwd)/audit:/audit \
            copex/audit do-audit "$parameters"

            __msg "Gnerating Code Sniffer - Coding Standard Output"

            if [ -d vendor/magento-ecg/coding-standard ]; then
                $DOCKER run \
                -v $(pwd):/project \
                -v $(pwd)/audit:/audit \
                copex/audit \
                bash -c "phpcs --config-set installed_paths /project/vendor/magento-ecg/coding-standard \
                ; phpcs --standard=Ecg /project/htdocs/app/code/community > /audit/community.cs.report \
                ; phpcs --standard=Ecg /project/htdocs/app/code/local > /audit/local.cs.report"
            fi
        else
            __msg "You need to specify magento version e.g. 1.9.2.2"
            __msg "use eg.: magento audit 1.9.2.2 folder for old src must be in folder htdocs"
        fi
    fi
}

function reindexAll() {
    if [ $MAGENTO_VERSION == 2 ]; then
       executeMagerun indexer:reindex
    else
       executeMagerun index:reindex:all
    fi
}

function watchTailwind() {
  readarray -t tailwind_directories < <(find "app/design/frontend" -type d -name "tailwind")

  if [ ${#tailwind_directories[@]} -eq 0 ]; then
    __error "No tailwind directories found."
    return 1
  fi

  local selected_dir
  if [ ${#tailwind_directories[@]} -eq 1 ]; then
    selected_dir="${tailwind_directories[0]}"
  else
    echo "Available Tailwind Directories:"
    for i in "${!tailwind_directories[@]}"; do
      index=$((i + 1))  # start with 1 instead of 0
      echo "[$index] ${tailwind_directories[$i]#app/design/frontend/}" | sed 's|/web/tailwind$||'
    done

    echo -n "Select the directory index to build/watch: "
    read -r selected_index

    # adjust index to be 0-based
    selected_index=$((selected_index - 1))

    if ! [[ "$selected_index" =~ ^[0-9]+$ ]] || [ "$selected_index" -ge "${#tailwind_directories[@]}" ]; then
      __err "Invalid selection. Exiting."
      return 1
    fi

    selected_dir="${tailwind_directories[$selected_index]}"
  fi

  local CONTAINER=$(getContainerNameByType "app")

  if [ ! -d "$selected_dir/node_modules" ]; then
    __info "Executing npm install"
    $DOCKER exec -u www-data -t "$CONTAINER" bash -c "cd \"$selected_dir\" && npm install"
  fi

  if [ -f "$selected_dir/tailwind.config.js" ] && [ -f "$selected_dir/package.json" ]; then
    tailwind_version=$(jq -r '.dependencies.tailwindcss' "$selected_dir/package.json")
    tailwind_major_version=$(echo "$tailwind_version" | sed -E 's/\^([0-9]+)\..*/\1/')

    echo "Tailwind Version: $tailwind_major_version"

    if [ "$tailwind_major_version" -gt 2 ]; then
      __info "Start watching tailwind for $selected_dir"
      $DOCKER exec -u www-data -ti "$CONTAINER" bash -c "cd \"$selected_dir\" && npm run watch"
    else
      __info "Start building tailwind for $selected_dir"
      $DOCKER exec -u www-data -ti "$CONTAINER" bash -c "cd \"$selected_dir\" && npm run build-dev"
    fi
  else
    __err "tailwind.config.js or package.json not found in $selected_dir"
  fi
}

function watchBrowsersync() {
  # First check if there are any Hyva themes (web/tailwind directories)
  readarray -t tailwind_directories < <(find "app/design/frontend" -type d -name "tailwind")

  if [ ${#tailwind_directories[@]} -gt 0 ]; then
    # Hyva themes found - use npm-based approach
    local selected_dir
    if [ ${#tailwind_directories[@]} -eq 1 ]; then
      selected_dir="${tailwind_directories[0]}"
    else
      echo "Available Hyva Theme Directories:"
      for i in "${!tailwind_directories[@]}"; do
        index=$((i + 1))  # start with 1 instead of 0
        echo "[$index] ${tailwind_directories[$i]#app/design/frontend/}" | sed 's|/web/tailwind$||'
      done

      echo -n "Select the theme directory index for BrowserSync: "
      read -r selected_index

      # adjust index to be 0-based
      selected_index=$((selected_index - 1))

      if ! [[ "$selected_index" =~ ^[0-9]+$ ]] || [ "$selected_index" -ge "${#tailwind_directories[@]}" ]; then
        __err "Invalid selection. Exiting."
        return 1
      fi

      selected_dir="${tailwind_directories[$selected_index]}"
    fi

    # Set up proxy URL for current project
    local PROTOCOL="http"
    local DOMAIN

    if [ "$parameters" ] && [[ "$parameters" == *.* ]]; then
      DOMAIN="$parameters"
    elif [ "$docker_compose_app_environment_DOMAIN" ]; then
      DOMAIN="$docker_compose_app_environment_DOMAIN"
    else
      local APPCONTAINER=$(getContainerNameByType "$APP")
      DOMAIN=$($DOCKER exec -ti $APPCONTAINER bash -c 'echo "$DOMAIN"|tr -d "\n"')
    fi

    # Check if SSL is enabled
    if [ "$docker_compose_app_environment_SSL_ON" == 1 ]; then
      PROTOCOL="https"
    fi

    local proxy="${PROTOCOL}://${DOMAIN}/"

    local CONTAINER=$(getContainerNameByType $APP)

    # Check if package.json exists in the tailwind directory
    if [ ! -f "$selected_dir/package.json" ]; then
      __err "package.json not found in $selected_dir"
      __info "Make sure your Hyva theme has npm dependencies installed"
      return 1
    fi

    # Check if node_modules exists, if not run npm install
    if [ ! -d "$selected_dir/node_modules" ]; then
      __info "Installing npm dependencies in $selected_dir"
      $DOCKER exec -u www-data -t "$CONTAINER" bash -c "cd \"$selected_dir\" && npm install"
    fi

    echo "Starting BrowserSync for Hyva theme: ${selected_dir#app/design/frontend/}" | sed 's|/web/tailwind$||'
    echo "Proxy URL: $proxy"
    echo "Working directory: $selected_dir"

    # Run npm run browser-sync with PROXY_URL environment variable (Hyva approach)
    __info "Running: PROXY_URL=\"$proxy\" npm run browser-sync"
    $DOCKER exec -u www-data -ti "$CONTAINER" bash -c "cd \"$selected_dir\" && PROXY_URL=\"$proxy\" npm run browser-sync"
  else
    # No Hyva themes found - use traditional browser-sync approach
    __info "No Hyva themes detected, using traditional BrowserSync approach"

    local proxy=$docker_compose_app_environment_DOMAIN
    if [ "$parameters" ]; then
      proxy=$parameters
    fi
    local containerName=$(getContainerNameByType $APP)
    local files="../.modman/**/*.css, ../.modman/**/*.js, skin/frontend/**/*.css, skin/frontend/**/*.js"
    if [ $MAGENTO_VERSION == 2 ]; then
      files="app/design/frontend/**/*.css, app/design/frontend/**/*.js, pub/static/frontend/**/*.css, pub/static/frontend/**/*.js"
    fi
    echo "Watching $files in $containerName"
    if [ "$($DOCKER exec ${containerName} which browser-sync)" > /dev/null ]; then
      $DOCKER exec -it ${containerName} \
        browser-sync start \
        --proxy "$proxy" \
        --files "${files}" \
        --no-open
    else
      __err "browser-sync not detected"
    fi
  fi
}



function showlogs(){
    tail -f "${rootDir}/var/log/system.log" "${rootDir}/var/log/exception.log"
}

function elasticsearch_clearreadonly() {
  local containerName=$(getContainerNameByType app)
  $DOCKER exec -ti -u www-data $containerName curl -X PUT "search:9200/_cluster/settings" -H 'Content-Type: application/json' -d'
                                              {
                                                "transient": {
                                                  "cluster.routing.allocation.disk.watermark.low": "30mb",
                                                  "cluster.routing.allocation.disk.watermark.high": "20mb",
                                                  "cluster.routing.allocation.disk.watermark.flood_stage": "10mb",
                                                  "cluster.info.update.interval": "1m"
                                                }
                                              }'
  $DOCKER exec -ti -u www-data $containerName curl -XPUT -H "Content-Type: application/json" http://search:9200/_all/_settings -d '{"index.blocks.read_only_allow_delete": null}'
  __msg ""
  __attn "Cleared readonly status of elasticsearch"
}

function application_installed() {
  if [ -f "${rootDir}/app/etc/env.php" ]; then
    echo "Magento 2"
  else
    echo ""
  fi
}

function install_application() {
  if declare -f "application_installed" > /dev/null; then
       installed=$(application_installed)
       if [ -z "$installed" ] || [[ "$parameters" == -f* ]]; then
          __attn "Installing Application $installed"
          install
       else
         __attn "Application ($installed) already installed"
       fi
     fi
}

function run_application() {
   checkImageDate
   runDockerCompose up -d "$@"
   install_application
   installQS
   if [[ -f "${rootDir}/config/index.php" && ! -L "pub/index.php" ]]; then
     __info "Symlinking index.php"
     ln -sf "../config/index.php" "pub/index.php"
   fi
}
function installQS() {
    if [ -d "${rootDir}/vendor/nunomaduro/phpinsights" ]; then
      __attn "Removing PHP Insights from composer because it is now executed inside a docker container"
      executeComposer remove nunomaduro/phpinsights --dev
    fi
    if [ ! -f "${rootDir}/config/phpinsights.php" ]; then
      __attn "Installing PHP Insights for Magento 2"
      cp "${SCRIPT_DIR}/config/qs/phpinsights.php" config/phpinsights.php
      local CODE_DIR="app/code"
      local INSIGHTS_FILE="config/phpinsights.php"
      DIRECTORIES=$(find "$CODE_DIR" -mindepth 1 -maxdepth 1 -type d -not -path "$CODE_DIR/CopeX" | sed 's/^/        "/;s/$/",/')
      NEW_EXCLUDE="
              'config',
              'app/etc/env.php',
              $DIRECTORIES"
      awk -v new_exclude="$NEW_EXCLUDE" '
      /^\s*'\''exclude'\''\s*=>\s*\[/ {
          print $0;                 # Print the matched line as is
          while (getline > 0) {     # Read the next line
              if ($0 ~ /\]/) {      # If closing bracket is found
                  print "        " new_exclude; # Insert the new exclude content
                  print "    ],";   # Add the closing bracket and comma
                  next;             # Skip to the next line outside the block
              }
          }
          next;
      }
      { print }                     # Print all other lines unchanged
      ' config/phpinsights.php > config/phpinsights.tmp && mv config/phpinsights.tmp config/phpinsights.php
      if command -v jq &> /dev/null; then
        local p_tmp=$parameters
        parameters="--format=json"
        JSON_STRING=$(passQS --format=json | sed 's/}[^}]*$/}/')
        parameters=$p_tmp
        CODE=$(echo "$JSON_STRING" | jq '.summary.code')
        COMPLEXITY=$(echo "$JSON_STRING" | jq '.summary.complexity')
        ARCHITECTURE=$(echo "$JSON_STRING" | jq '.summary.architecture')
        STYLE=$(echo "$JSON_STRING" | jq '.summary.style')

        # Use sed to replace the values in the PHP config file
        sed -i \
            -e "s/'min-quality' *=> *[0-9.]*/'min-quality' => $CODE/" \
            -e "s/'min-complexity' *=> *[0-9.]*/'min-complexity' => $COMPLEXITY/" \
            -e "s/'min-architecture' *=> *[0-9.]*/'min-architecture' => $ARCHITECTURE/" \
            -e "s/'min-style' *=> *[0-9.]*/'min-style' => $STYLE/" \
            "$INSIGHTS_FILE"
      fi
    fi
    if [ ! -d "/tmp/phpinsights" ]; then
      mkdir "/tmp/phpinsights"
      chmod 777 /tmp/phpinsights
    fi
    installQSCommitHook
}

function fixQS(){
    if [ -n "$PHP_VERSION" ]; then
      installQS
      $DOCKER run --rm -it -v "${rootDir}":/app -w /app -u $(id -u ${USER}):$(id -g ${USER}) jakzal/phpqa:php${PHP_VERSION:-8.1}-alpine  phpinsights analyse --fix --composer=/app/composer.lock --config-path=config/phpinsights.php $parameters
    else
      __err "No PHP Version given"
    fi
}

function checkQS() {
    if [ -n "$PHP_VERSION" ]; then
      installQS
      $DOCKER run --rm -it -v "${rootDir}":/app -w /app -u $(id -u ${USER}):$(id -g ${USER})  jakzal/phpqa:php${PHP_VERSION:-8.1}-alpine  phpinsights analyse --composer=/app/composer.lock --config-path=config/phpinsights.php $parameters
    else
      __err "No PHP Version given"
    fi
}

function passQS(){
    if [ -n "$PHP_VERSION" ]; then
      installQS
      $DOCKER run --rm -v "${rootDir}":/app -w /app -u $(id -u ${USER}):$(id -g ${USER})  jakzal/phpqa:php${PHP_VERSION:-8.1}-alpine  phpinsights analyse --composer=/app/composer.lock --config-path=config/phpinsights.php --no-interaction -s $parameters && __attn "QS passed" || __err "QS error"
    else
      __err "No PHP Version given"
    fi
}

function installQSCommitHook(){
    cd $rootDir
    if [ -f .githooks/pre-commit ] && [ ! -f .githooks/pre-push ]; then
      rm .githooks/pre-commit
    fi
    if [ ! -f .githooks/pre-push ]; then
          mkdir -p .githooks
          # Define the content of the pre-commit script
          pre_content='#!/bin/sh
if [ ! -f .git/MERGE_HEAD ]
then
    NOCOLORS=1 c qs:pass -q

    # Exit with non-zero status if any of the checks failed
    if [ $? -ne 0 ]; then
        echo "Pre-push checks failed."
        exit 1
    fi
fi
          '
          echo "$pre_content" >> "$rootDir/.githooks/pre-push"
          chmod +x .githooks/pre-push
          git config core.hooksPath .githooks
          __attn "Pre-push hook has been set up successfully."
    fi
}

function application_help() {
      __mainhead "Magento Commands"
      __help "m|magento" "Executes the bin/magento script"
      __help "magerun" "Executes n98magerun in the magento root directory"
      __help "cache:clean" "Cleans the magento cache"
      __help "cache:flush" "alias for cache:clean"
      __help "reindexall" "Reindexes Database"
      __help "setbaseurl" "Set base url from given container or from docker-compose.yml"
      __help "resetadminpassword" "Resets the admin password to default values"
      __help "rm:generated" "Remove the generated files in magento 2"
      __help "media:download" "Download media from server - done with capistrano"
      __help "deployStaticContent" "setup:static-content:deploy \$params and fixpermissions"
      __head "import & export"
      __help "db:import" "Imports the magento db via n98 of the given file"
      __help "db:export" "Exports the database from the docker container to db/export/current_dump.sql.gz"
      __help "db:download" "Download database from server - done with deployer [-v for verbose output]"
      __head "permissions"
      __help "fixpermissions" "Fixes permissions for media, sitemaps and var folder"
      __help "fixpermissions:all" "Fixes permissions everything in the root folder"
      __head "watch"
      __help "watch:cache" "Watch cache for changes (needs mage2tv/magento-cache-clean)"
      __help "watch:static" "BrowserSync watch static file changes (css,js) - use port 3000"
      __help "watch:static:live" "Livereload to watch static files (grunt-based). Parameter 1 to run exec before watch"
      __help "watch:tailwind" "Watch Tailwind"
      __help "watch:browsersync" "BrowserSync - uses npm for Hyva themes, traditional for others"
      __help "show:logs" "Tail magento system and exception logs"
      __help "elastic:readonly" "Cleares readonly status of elasticsearch"
      __help "qs:install" "Installs PHP Insights"
      __help "qs:fix" "Try fixing PHP Issues"
      __help "qs:check" "Check if code has errors"
      __help "qs:pass" "Check if code would pass CI Pipeline"
      __help "qs:installhook" "Installs Git Hook for ci:check"
      if [ $MAGENTO_VERSION == 1 ]; then
        __head "Magento 1"
        __help "magepack:generate" "Generate Magepack config (only available for Magento 1)"
        __help "magepack:bundle" "Bundles the Magepack config (only available for Magento 1)"
        __help "audit" "Audit magento instance by given magento version"
        __help "modman:create" "Creates a modman module from an existing extension using meff and modman-generate"
        __help "modman:create:all" "Creates modman modules for all local and community extensions"
        __help "initphpunit" "Installs the phpunit database"
      fi
}

source "$SCRIPT_DIR/app/magento/detectMagentoVersion.sh"
if [ $MAGENTO_VERSION == 1 ]; then
  source "$SCRIPT_DIR/app/magento/magepack.sh"
  source "$SCRIPT_DIR/app/magento/modman.sh"
fi

APPLICATION_EXECUTED=1
case "${action}" in

      magerun)
      start
      magerun
      ;;
      db:import)
      start
      importdb
      ;;

      db:export)
      start
      exportdb
      ;;

      db:download)
      downloaddb
      ;;

      media:download)
      downloadmedia
      ;;

      m|magento)
      start
      executeMagento2
      ;;

      deployStaticContent)
      start
      executeMagento2 setup:static-content:deploy $@
      fixPermissions
      ;;

      cc|cache:clean)
      start
      cleancache
      ;;

      cache:flush)
      start
      flushcache
      ;;

      initphpunit)
      installPhpUnitDB
      ;;

      audit)
      audit
      ;;

      setbaseurl)
      start
      setBaseUrl
      ;;

      fixpermissions)
      fixPermissions
      __msg "Permissions fixed"
      ;;

      fixpermissions:all)
      fixPermissionsAll
      __msg "ALL Permissions fixed"
      ;;

      resetadminpassword)
      start
      resetAdminPassword
      ;;

      reindexall)
      start
      reindexAll
      ;;

      modman:create)
      modmanCreateSingle
      ;;

      modman:create:all)
      modmanCreateAll
      ;;

      module:enable)
      start
      executeMagento2 module:enable $@
      executeMagento2 setup:upgrade
      rmgenerated
      ;;

      module:disable)
      start
      executeMagento2 module:disable $@
      executeMagento2 setup:upgrade
      rmgenerated
      ;;

      rm:generated)
      rmgenerated
      ;;

      watch:cache)
      start
      watchCache
      ;;

      watch:static)
      start
      watchStatic
      ;;

      watch:static:live)
      start
      watchStaticLivereload
      ;;

      watch:tailwind)
      start
      watchTailwind
      ;;

      watch:browsersync)
      start
      watchBrowsersync
      ;;

      show:logs)
      showlogs
      ;;

      elastic:readonly)
      start
      elasticsearch_clearreadonly
      ;;

      magepack:generate)
      magepackGenerate
      ;;

      magepack:bundle)
      magepackBundle
      ;;

      qs:install)
      installQS
      ;;

      qs:fix)
      fixQS
      ;;

      qs:check)
      checkQS
      ;;

      qs:pass)
      passQS
      ;;

      qs:installhook)
      installQSCommitHook
      ;;

      *)
      APPLICATION_EXECUTED=0
      ;;
esac