cron:
  image: copex/cron
  links:
    - "cache:rediscache"
    - "sessions:redissession"
    - "fullpagecache:redisfullpagecache"
    - "mysql:mysql"
  volumes_from:
    - data
app:
  image: copex/nginx-php-fpm
  ports:
    - "80:80"
    - "443:443"
  links:
    - "cache:rediscache"
    - "sessions:redissession"
    - "fullpagecache:redisfullpagecache"
    - "mysql:mysql"
  domainname: magento.copex.io
  environment:
    DOMAIN: magento.copex.io
    MAGENTO_ROOT: /var/www/htdocs
    MAGENTO_DEVELOPERMODE: 0
  volumes_from:
    - data
mysql:
  image: mariadb
  environment:
    MYSQL_ROOT_PASSWORD: "c0peX!§*1337*§!"
    MYSQL_USER: "user"
    MYSQL_PASSWORD: "pwd"
    MYSQL_DATABASE: "db_pwd"
  volumes_from:
    - dbdata
cache:
  image: redis:latest
fullpagecache:
  image: redis:latest
sessions:
  image: redis:latest
data:
  image: tianon/true
  volumes:
    - /var/www:/var/www
dbdata:
  image: tianon/true
  volumes:
    - var/lib/mysql
#tools:
#  image: copex/tools
#  links:
#    - "cache:rediscache"
#    - "sessions:redissession"
#    - "fullpagecache:redisfullpagecache"
#    - "mysql:mysql"
#  volumes_from:
#    - data
authorizedkeys:
  image: tutum/authorizedkeys
  deployment_strategy: every_node
  autodestroy: always
  environment:
    - AUTHORIZED_KEYS=ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC9enn03n3PQf3AS+sCJ5D/6GWBooEijltcP0A8antVZMvRED7BsQRdC7GQEiEQ39Rt9uT7vzR1bopCZC2uu2Yu7GX7K4DxOhjGnOPC3yllkEECNLm0q/sspQbzILCQWfzIQDTLZxWom9b4dx8u7+4X0sYeF63iLd9Udwa/vHKa9Cg2Vs4ErHnX6+ovTUTAfOWsZeTu5mBWXfGz3e4X1fnUuoeltZuYKiJiB+AFZfogvi8sxrwSGvmIn327WUxJ1jTeg3FYM+V6Yn1R2WKJUiag1aqxCB5U5+Sg6a1VJ17TBBdaM0wi/2q6q8ksWe3MIgEIbUMZYfhiyn16oLTwg/FWHKLLYy62hdYta4mq4pAY4LEbAbVb9Xf4Rr/qOgzAWl8wwyQN2aLgmDv+ilDqmKtu18FyrW6F21zI0E1cB/mYYIrO8bfS/cBSaSymBhZZs+vRs4yLOgsPCAuNjs13TraaWITVxJuZpJ7FboCHqSVC9j4ZKmY7CRl79xJ+Y+Q0TSFZQGQ0O+eoazjHjtCw181IHeusRqiyrvZq5wnrcwtTv7g9b7P2QM2A6dcW1J+JoQBw+CXelp6BaIdfmaoV4j8SDcnkqNx1zXSxwPi2ihzXEemrRJ6sPrMxgPvyqMGpHwEiSnr87jUN5+mdiE3SbhF4RHcBH14P6k4lXMDTvCGPWQ== pointi@pointi-nb
  volumes:
    - /root:/user:rw