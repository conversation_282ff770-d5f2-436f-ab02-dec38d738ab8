#!/usr/bin/env bash
if ! ( hash "$DOCKERCOMPOSE" 2>/dev/null); then
    __err "docker-compose is not installed" && exit 1
fi
DOCKER_COMPOSE_VERSION="$($DOCKERCOMPOSE version | grep -oE '[0-9\.]+' | head -n1)"
if ! ( test "$(printf "$DOCKER_COMPOSE_VERSION\n$REQUIRED_DOCKER_COMPOSE" | sort -rV | head -n 1)" == "$DOCKER_COMPOSE_VERSION" ); then
   __err "docker-compose version should be $REQUIRED_DOCKER_COMPOSE or higher ($DOCKER_COMPOSE_VERSION installed)" && exit 1
fi

#source "$SCRIPT_DIR/commands/setup/ca.cmd"
source "$SCRIPT_DIR/commands/setup/resolver.cmd"
source "$SCRIPT_DIR/commands/setup/tunnel.cmd"
source "$SCRIPT_DIR/commands/setup/docker.cmd"

chmod +x $SCRIPT_DIR/magento.sh
sudo ln -fs $SCRIPT_DIR/magento.sh /usr/local/bin/magento

date > "${MAGENTO_HOME_DIR}/.installed"