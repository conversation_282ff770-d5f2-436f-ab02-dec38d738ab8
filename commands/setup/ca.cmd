#!/usr/bin/env bash

if [[ ! -d "${SSL_DIR}/rootca" ]]; then
    mkdir -p "${SSL_DIR}/rootca"/{certs,crl,newcerts,private}

    touch "${SSL_DIR}/rootca/index.txt"
    echo 1000 > "${SSL_DIR}/rootca/serial"
fi

# create CA root certificate if none present
if [[ ! -f "${SSL_DIR}/rootca/private/ca.key.pem" ]]; then
  __msg "==> Generating private key for local root certificate"
  openssl genrsa -out "${SSL_DIR}/rootca/private/ca.key.pem" 2048
fi

if [[ ! -f "${SSL_DIR}/rootca/certs/ca.cert.pem" ]]; then
  __msg "==> Signing root certificate (Proxy Local CA)"
  openssl req -new -x509 -days 7300 -sha256 -extensions v3_ca \
    -config "${SCRIPT_DIR}/config/openssl/rootca.conf"        \
    -key "${SSL_DIR}/rootca/private/ca.key.pem"        \
    -out "${SSL_DIR}/rootca/certs/ca.cert.pem"         \
    -subj "/C=US/O=Proxy Local CA"
fi

## trust root ca differently on Fedora, Ubuntu and macOS
if [[ "$OSTYPE" == "linux-gnu" ]] \
  && [[ -d /etc/pki/ca-trust/source/anchors ]] \
  && [[ ! -f /etc/pki/ca-trust/source/anchors/proxy-local-ca.cert.pem ]] \
  ## Fedora/CentOS
then
  __msg "==> Trusting root certificate (requires sudo privileges)"
  sudo cp "${SSL_DIR}/rootca/certs/ca.cert.pem" /etc/pki/ca-trust/source/anchors/proxy-local-ca.cert.pem
  sudo update-ca-trust
  sudo update-ca-trust enable
elif [[ "$OSTYPE" == "linux-gnu" ]] \
  && [[ -d /usr/local/share/ca-certificates ]] \
  && [[ ! -f /usr/local/share/ca-certificates/proxy-local-ca.crt ]] \
  ## Ubuntu/Debian
then
  __msg "==> Trusting root certificate (requires sudo privileges)"
  sudo cp "${SSL_DIR}/rootca/certs/ca.cert.pem" /usr/local/share/ca-certificates/proxy-local-ca.crt
  sudo update-ca-certificates
elif [[ "$OSTYPE" == "darwin"* ]] \
  && ! security dump-trust-settings -d | grep 'Proxy Local CA' >/dev/null \
  ## Apple macOS
then
  __msg "==> Trusting root certificate (requires sudo privileges)"
  sudo security add-trusted-cert -d -r trustRoot \
    -k /Library/Keychains/System.keychain "${SSL_DIR}/rootca/certs/ca.cert.pem"
fi


## sign certificate used by services run on magento.local sub-domains
if [[ ! -f "${SSL_DIR}/certs/magento.local.crt.pem" ]]; then
  "${SCRIPT_DIR}/magento.sh" sign:certificate magento.local
fi