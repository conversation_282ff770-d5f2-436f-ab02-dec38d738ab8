#!/usr/bin/env bash

## generate rsa keypair for authenticating to sshd service
if [[ ! -f "${MAGENTO_HOME_DIR}/tunnel/ssh_key" ]]; then
  __msg "==> Generating rsa key pair for tunnel into sshd service"
  mkdir -p "${MAGENTO_HOME_DIR}/tunnel"
  ssh-keygen -b 2048 -t rsa -f "${MAGENTO_HOME_DIR}/tunnel/ssh_key" -N "" -C "<EMAIL>"
fi

## if host machine does not have composer installed, this directory will otherwise be created by docker with root:root
## causing problems so it's created as current user to avoid composer issues inside environments given it is mounted
if [[ ! -d ~/.composer ]]; then
  mkdir ~/.composer
fi


## since bind mounts are native on linux to use .pub file as authorized_keys file in tunnel it must have proper perms
if [[ "$OSTYPE" == "linux-gnu" ]] && [[ "$(stat -c '%U' "${MAGENTO_HOME_DIR}/tunnel/ssh_key.pub")" != "root" ]]; then
  __msg "Need to set root permissions for ssh_key.pub in tunnel directory"
  sudo chown root:root "${MAGENTO_HOME_DIR}/tunnel/ssh_key.pub"
fi

if ! grep '## MAGENTO START ##' /etc/ssh/ssh_config >/dev/null; then
  echo "==> Configuring sshd tunnel in host ssh_config (requires sudo privileges)"
  cat <<-EOF | sudo tee -a /etc/ssh/ssh_config >/dev/null

		## MAGENTO START ##
		Host tunnel.magento.local
		  HostName 127.0.0.1
		  User user
		  Port 2222
		  IdentityFile ~/.magento/tunnel/ssh_key
		## MAGENTO END ##
		EOF
fi