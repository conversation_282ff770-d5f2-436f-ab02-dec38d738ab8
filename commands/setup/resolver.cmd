#!/usr/bin/env bash

## configure resolver for .local domains; allow linux machines to prevent
## from touching dns configuration if need be since unlike macOS there is not
## support for resolving only *.test *.dev domains via /etc/resolver/test settings
if [[ "$OSTYPE" == "linux-gnu" ]]; then
  if systemctl status NetworkManager | grep 'active (running)' >/dev/null \
    && ! grep '^nameserver 127.0.0.1$' /etc/resolv.conf >/dev/null
  then
    __msg "==> Configuring resolver for .dev .test domains (requires sudo privileges)"
    if ! sudo grep '^prepend domain-name-servers 127.0.0.1;$' /etc/dhcp/dhclient.conf >/dev/null 2>&1; then
      __msg "  + Configuring dhclient to prepend dns with 127.0.0.1 resolver (requires sudo privileges)"
      DHCLIENT_CONF=$'\n'"$(sudo cat /etc/dhcp/dhclient.conf 2>/dev/null)" || DHCLIENT_CONF=
      DHCLIENT_CONF="prepend domain-name-servers 127.0.0.1;${DHCLIENT_CONF}"
      echo "${DHCLIENT_CONF}" | sudo tee /etc/dhcp/dhclient.conf
      sudo systemctl restart NetworkManager
    fi

    if [[ ! -f /etc/NetworkManager/conf.d/dnsmasq.conf ]]; then
      printf "[main]\ndns=dnsmasq" | sudo tee /etc/NetworkManager/conf.d/dnsmasq.conf >/dev/null
      sudo systemctl restart NetworkManager
    fi


    if [[ ! -f /etc/NetworkManager/dnsmasq.d/local.conf ]]; then
      printf "address=/.test/127.0.0.1\naddress=/.dev/127.0.0.1" | sudo tee /etc/NetworkManager/dnsmasq.d/local.conf >/dev/null
      sudo systemctl restart NetworkManager
    fi

    if ! sudo grep 'files dns mdns4_minimal \[NOTFOUND=return\] myhostname' /etc/nsswitch.conf >/dev/null 2>&1; then
      __msg "  + Configuring nsswitch"
      sudo $SED -i 's/files mdns4_minimal \[NOTFOUND=return\] dns myhostname/files dns mdns4_minimal \[NOTFOUND=return\] myhostname/g' /etc/nsswitch.conf
    fi

    ## When systemd-resolvd is used (as it is on Ubuntu by default) check the resolv config mode
    if systemctl status systemd-resolved | grep 'active (running)' >/dev/null \
      && [[ -L /etc/resolv.conf ]] \
      && [[ "$(readlink /etc/resolv.conf)" != "../run/systemd/resolve/resolv.conf" ]]
    then
      __msg "  + Configuring systemd-resolved to use dhcp settings (requires sudo privileges)"
      __msg "    by pointing /etc/resolv.conf at resolv.conf vs stub-resolv.conf"
      sudo ln -fsn ../run/systemd/resolve/resolv.conf /etc/resolv.conf
    fi
  fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
  if [[ ! -f /etc/resolver/test ]]; then
    __msg "==> Configuring resolver for .local domains (requires sudo privileges)"
    if [[ ! -d /etc/resolver ]]; then
        sudo mkdir /etc/resolver
    fi
    echo "nameserver 127.0.0.1" | sudo tee /etc/resolver/test >/dev/null
  fi
else
  __err "WARNING: Use of dnsmasq is not supported on this system; entries in /etc/hosts will be required"
fi
