#!/usr/bin/env bash

if [[ ! "$($DOCKER network ls | $GREP web)" ]]; then
  __attn "Creating web network ..."
  $DOCKER network create web
fi
if [[ ! "$($DOCKER volume ls | $GREP composer-cache)" ]]; then
  __attn "Creating volume composer-cache ..."
  $DOCKER volume create composer-cache
  $DOCKER run --rm -v composer-cache:/mnt busybox mkdir -p /mnt/var/www/.composer
  $DOCKER run --rm -v composer-cache:/mnt busybox chmod -R 777 /mnt/var/www/.composer
fi