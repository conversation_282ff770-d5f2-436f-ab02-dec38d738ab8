#!/bin/sh

workingDir=$(pwd)
scriptPath="${workingDir}/c.sh"

cd /usr/local/bin

sudo rm -f c copex magento m

sudo ln -sf ${scriptPath} c
sudo ln -sf c copex
sudo ln -sf c magento
sudo ln -sf c m

# check if group already exists. Otherwise create it
if [ ! $(getent group docker) ]; then
  echo "Adding current user to docker group..."
  sudo groupadd docker
fi
if ! id -nG "$USER" | grep -qw "docker"; then
  sudo usermod -aG docker $USER
  echo "Done. Restart required to apply group changes!"
fi

