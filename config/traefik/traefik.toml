defaultEntryPoints = ["http", "https"]

# TODO: Get rid of this (m2c images are redirecting all 8000 requests via 301 currently, requiring use of 8443 to work)
insecureSkipVerify = true

[entryPoints]
  [entryPoints.http]
  address = ":80"
    [entryPoints.http.redirect]
    entryPoint = "https"
  [entryPoints.https]
  address = ":443"
    [entryPoints.https.tls]
      [[entryPoints.https.tls.certificates]]
      certFile = "/etc/ssl/certs/magento.local.crt.pem"
      keyFile = "/etc/ssl/certs/magento.local.key.pem"