cron:
  image: copex/cron
  links:
    - "cache:rediscache"
    - "sessions:redissession"
    - "fullpagecache:redisfullpagecache"
    - "mysql:mysql"
  volumes_from:
    - data
app:
  image: copex/nginx-php-fpm:dev
  ports:
    - "80:80"
    - "443:443"
    - "1080:1080"
  links:
    - "cache:rediscache"
    - "sessions:redissession"
    - "fullpagecache:redisfullpagecache"
    - "mysql:mysql"
  environment:
    DOMAIN: www.magento.local
    MAGENTO_ROOT: /var/www/htdocs
    MAGENTO_DEVELOPERMODE: 0
  volumes_from:
    - data
mysql:
  image: mariadb
  environment:
    MYSQL_ROOT_PASSWORD: "r00t"
    MYSQL_USER: "magento"
    MYSQL_PASSWORD: "magento"
    MYSQL_DATABASE: "magento"
  volumes_from:
    - dbdata
  ports:
    - "3306:3306"
cache:
  image: redis:latest
fullpagecache:
  image: redis:latest
sessions:
  image: redis:latest
data:
  image: tianon/true
  volumes:
    - .:/var/www
dbdata:
  image: tianon/true
  volumes:
    - /var/lib/mysql
#tools:
#  image: copex/tools
#  links:
#    - "cache:rediscache"
#    - "sessions:redissession"
#    - "fullpagecache:redisfullpagecache"
#    - "mysql:mysql"
#  volumes_from:
#    - data