#!/usr/bin/env bash
set -e
trap '>&2 printf "\n\e[01;31mERROR\033[0m: Command \`%s\` on line $LINENO failed with exit code $?\n" "$BASH_COMMAND"' ERR

readonly SCRIPT_DIR="$(
  cd "$(dirname "$(realpath "${BASH_SOURCE[0]}")")" >/dev/null \
  && pwd
)"

#
# A control-script for managing the docker-infrastructure components for Magento


# The first parameter is the action name
action=$1

EXECUTED=1

# All other arguments are parameters
if [ "$#" -gt "1" ]; then
    shift
    parameters=$@
fi

source "$SCRIPT_DIR/utils/io.sh"
source "$SCRIPT_DIR/utils/utils.sh"
source "$SCRIPT_DIR/utils/vars.sh"
source "$SCRIPT_DIR/utils/ssh.sh"
source "$SCRIPT_DIR/utils/docker.sh"
source "$SCRIPT_DIR/utils/mysqltuner.sh"


# Switch into the project directory
cd $rootDir

function checkApplication(){
  if [ -f "${rootDir}/composer.json" ]; then
    if grep -q '"magento/product' "${rootDir}/composer.json" ; then
      APPLICATION="magento"
    fi
  else
     if [ -f "${rootDir}/config/application" ]; then
          APPLICATION=$(cat ${rootDir}/config/application)
     fi
  fi
  if [ -f "$SCRIPT_DIR/app/$APPLICATION.sh" ]; then
    source "$SCRIPT_DIR/app/$APPLICATION.sh"
  fi
}

function executeComposer() {
    local containerType="$APP"
    local containerName=$(getContainerNameByType $containerType)
    if [ -z "$containerName" ];
    then
        __err "Cannot determine the name of the container." >&2
        return 1
    fi
    checkAuthJson
    local command_prefix="XDEBUG_MODE=off COMPOSER_MEMORY_LIMIT=-1"
    __info "execute composer on container $containerName with user $DOCKER_USER"
    local command="$command_prefix composer $@"
    if [ $MAGENTO_VERSION == 1 ]; then
        command="cd .. && $command"
    fi
    $DOCKER exec -it -u $DOCKER_USER $containerName bash -c "$command"
    return 0
}

function checkAuthJson() {
    if ! [[ -f "${rootDir}/auth.json" ]]; then
        __err "${rootDir}/auth.json missing"
        exit 1
    fi
}

function enterContainer() {
    local containerType="$1"
    local user="$2"
    local containerName=$(getContainerNameByType $containerType)
    if [ "$2" = "" ]
      then
        $DOCKER exec -ti -u www-data $containerName bash
      else
        $DOCKER exec -ti -u $user $containerName bash
      fi
    return 0
}


function runContainerRoot(){
    local containerType="$1"
    local containerName=$(getContainerNameByType $containerType)
    $DOCKER exec -ti -u root $containerName ${@:2}
    return 0
}

function runContainer(){
    local containerType="$1"
    local containerName=$(getContainerNameByType $containerType)
    $DOCKER exec -ti -u www-data $containerName ${@:2}
    return 0
}

function startForce(){
  stopOtherContainers
  if declare -f "run_application" > /dev/null; then
    run_application "$parameters"
  else
    runDockerCompose up -d "$parameters"
  fi
}

function start() {
    stopOtherContainers
    if ! runDockerCompose ps --filter="status=running" -q 2>/dev/null | grep -q .; then
          if declare -f "run_application" > /dev/null; then
            run_application "$parameters"
          else
            runDockerCompose up -d "$parameters"
          fi
    fi
}

function stop() {
    runDockerCompose stop "$parameters"
}

function stopall() {
    RUNNING_CONTAINERS=$($DOCKER ps -q | wc -l)
    if [ $RUNNING_CONTAINERS -gt 0 ]; then
      __attn "Stopping all containers";
      $DOCKER stop $($DOCKER ps -q)
    fi
}

function restart() {
    if [ ! "$parameters" ]; then
        runDockerCompose restart
    else
        runDockerCompose restart "$parameters"
    fi
}

function status() {
    runDockerCompose ps
}

function runDockerCompose(){
    # local currentDirectory=$(pwd)
    cd ${rootDir}
    if [[ -e "${docker_compose_file/.yml/.local.yml}" ]]; then
      export COMPOSE_FILE="$docker_compose_file:${docker_compose_file/.yml/.local.yml}"
    elif [[ -e "${docker_compose_file/.yml/.override.yml}" ]]; then
      export COMPOSE_FILE="$docker_compose_file:${docker_compose_file/.yml/.override.yml}"
    else
      export COMPOSE_FILE="$docker_compose_file"
    fi
    $DOCKERCOMPOSE --env-file "$env" $@
    # cd $(currentDirectory)
}

function runInstallApplication() {
    parameters="-f $parameters"
    if declare -f "install_application" > /dev/null; then
         install_application
    fi
}

function runCustomInstallScript() {
    if [ -f "${rootDir}/config/install.sh" ]; then
        __info "Running custom install script"
        chmod +x "${rootDir}/config/install.sh"
        ${rootDir}/config/install.sh
    else
      __err "No custom install scripts available"
    fi
}

function stats() {
    # check if sed is available
    if [ -z "$SED" ];
    then
        __err "Stats requires 'sed'. The tool was not found on your system." >&2
        return 1
    fi

    $DOCKER ps -q | $XARGS $DOCKER inspect --format '{{.Name}}' | $SED 's:/::' | $XARGS $DOCKER stats
}

function whatsmyip() {
  curl -s http://whatismyip.akamai.com/
}

function cleanredis() {
    local containerType="redis"
    if [ "$parameters" ]; then
      containerType=$parameters
    fi
    local containerName=$(getContainerNameByType $containerType)
    if [ -z "$containerName" ];
    then
        __err "Cannot determine the name of the container." >&2
        return 1
    fi

    $DOCKER exec -ti $containerName redis-cli flushall
}

function composer() {
    executeComposer $parameters
}

function enter() {
    enterContainer $parameters
}

function logs () {
    runDockerCompose logs -f --tail=200 $parameters
}

function destroy () {
    local rmVolumes=""
    while true; do
        read -p "Do you want to remove the volumes? (y|n)" yn
        case $yn in
            [Yy]* ) rmVolumes="-v"; break;;
            [Nn]* ) break;;
            * ) echo "Please answer [y]es or [n]o.";;
        esac
    done
    runDockerCompose down --remove-orphans ${rmVolumes}
}


function selfupdate () {
    cd $SCRIPT_DIR
    git pull
    ./install.sh
}

function checkDocker(){
    if [[ ! "$($DOCKER network ls | $GREP web)" ]]; then
      __attn "Creating web network ..."
      $DOCKER network create web
    fi
    if [[ ! "$($DOCKER network ls | $GREP backend)" ]]; then
      __attn "Creating web backend ..."
      $DOCKER network create backend
    fi
    if [[ ! "$($DOCKER volume ls | $GREP composer-cache)" ]]; then
      __attn "Creating volume composer-cache ..."
      $DOCKER volume create composer-cache
    fi
}



function copex_help() {
    __mainhead "CopeX Tools commands"
    __help "selfupdate" "Update this toolset"
    __help "blackfire" "Run blackfire container"
    __help "signcertificate" "Sign ssl certificate"
    __head "run"
    __help "setup" "Install this toolset"
    __help "start" "Starts the docker containers (and triggers the installation if magento is not yet installed)"
    __help "stop" "Stops all docker containers of this setup"
    __help "sa|stop:all" "Stops all running docker containers"
    __help "ss|stop:start|startit" "Stops all running docker containers and starts current containers"
    __help "restart" "Restarts all docker containers"
    __help "status " "Prints the status of all docker containers"
    __help "stats" "Displays live resource usage statistics of all containers"
    __help "logs" "Show logs of all containers"
    __help "enter" "Enters the bash of a given container type (e.g. app, mysql) second parameter is the user (e.g. magento enter app www-data) "
    __help "run|runroot" "Runs a command in the given container (e.g. app, mysql)"
    __help "destroy" "Stops all containers and removes all data"
    __help "install" "Installs detected Application on local environment"
    __help "install:custom" "Executes the install.sh in config folder (automatically executed when calling install)"
    __help "cache:clean:redis" "Clean redis cli"
    __head "composer"
    __help "composer" "Executes composer in the magento root directory"
    __head "docker"
    __help "docker:remove:old" "Remove docker containers that haven't been started within 2 months"
    __help "docker:date" "Get container date"
    __help "docker:volume:lookup" "See where a volume is attached to a container by id"
    __help "docker:volume:cp" "Copy docker volume (eg. database) to another volume"
    __help "docker:volume:info" "Get info of all existing docker volumes"
    __help "docker:image:update" "Update CopeX dev image"
    __head "ssh"
    __help "ssh:tunnel" "Tunnel any host through copex.io"
    __help "ssh:tunnel:config" "Execute tunnel config in config/tunnel.sh from project"
    __help "ssh:copy:directory" "Copies a directory from server A to server B (generates temp ssh-key)"
    __help "whatsmyip" "Show your own IP"
}

checkDocker
checkApplication

case "${action}" in

    setup)
      source "$SCRIPT_DIR/commands/setup.cmd"
    ;;

    start)
    startForce
    ;;

    stop)
    stop
    ;;

    sa|stop:all)
    stopall
    ;;

    ss|stop:start|startit)
    stopall
    start
    ;;

    restart)
    stopOtherContainers
    restart
    ;;

    status)
    start
    status
    ;;

    stats)
    start
    stats
    ;;

    install)
    start
    runInstallApplication
    runCustomInstallScript
    ;;

    install:custom)
    runCustomInstallScript
    ;;

    composer)
    start
    composer
    ;;

    enter)
    start
    enter
    ;;

    run)
    start
    runContainer $parameters
    ;;

    runroot)
    start
    runContainerRoot $parameters
    ;;

    cache:clean:redis)
    start
    cleanredis
    ;;

    logs)
    start
    logs
    ;;

    destroy)
    destroy
    ;;

    selfupdate)
    selfupdate
    ;;

    docker:date)
    getContainerDate $@
    ;;

    docker:volume:lookup)
    dockerVolumeLookup $@
    ;;

    docker:volume:info)
    dockerVolumeInfo $@
    ;;

    docker:volume:cp)
    dockerVolumeCp $@
    ;;

    docker:remove:old)
    dockerremoveold
    ;;

    docker:image:update)
    dockerimageupdate
    ;;

    blackfire)
    runBlackfire
    ;;

    mysqltuner)
    mysqltuner
    ;;

    ssh:tunnel)
    sshTunnel
    ;;

    ssh:tunnel:config)
    sshTunnelConfig
    ;;

    ssh:copy:directory)
    transfer_from_server_to_server $@
    ;;

    whatsmyip)
    whatsmyip
    ;;

    *)
      EXECUTED=0
    ;;
esac

if [[ $APPLICATION_EXECUTED == 0 && $EXECUTED == 0 ]]; then
  copex_help
  if declare -f "application_help"> /dev/null; then
    application_help
  fi
  __foot
fi