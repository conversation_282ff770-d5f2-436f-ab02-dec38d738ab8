Install by executing ./install.sh

The magento bash script will be symlinked to the /usr/local/bin directory

Howto use:
==
For Help enter "magento"
To execute the commands enter magento COMMAND

Importing / Exporting databases
The Import command is looking for *.tar.gz files inside the folders db/import/
The Export command exports the current database inside the container to the file db/export/current_dump.sql.gz

Configuration
==
use another application by creating an /config/application file that contains the application e.g. magento which needs to be defined in this tool under utils/magento.hs

Available commands
==
  execute the command without any commands to see all available commands