#!/usr/bin/env bash
[[ ! ${SCRIPT_DIR} ]] && >&2 exit 1


SCRIPTNAME="$(basename $0)"
if [ -z "${NOCOLORS+x}" ]; then
  # bash colors
  COLOR_RED="$(tput setaf 1; tput bold)"
  COLOR_GREEN="$(tput setaf 2; tput bold)"
  COLOR_YELLOW="$(tput setaf 3; tput bold)"
  COLOR_BLUE="$(tput setaf 4; tput bold)"
  COLOR_MAGENTA="$(tput setaf 5; tput bold)"
  COLOR_CYAN="$(tput setaf 6; tput bold)"
  COLOR_WHITE="$(tput setaf 7; tput bold)"
  COLOR_RESET="$(tput sgr0)"
fi

function __attn() { printf "${COLOR_GREEN}$*${COLOR_RESET}\n"; }

function __msg() { printf "$*\n"; }

function __info() { printf "${COLOR_YELLOW}$*${COLOR_RESET}\n"; }

function __err() { printf "${COLOR_RED}$*${COLOR_RESET}\n";}

function __value() { printf "${COLOR_GREEN}%-30s${COLOR_RESET}\t${COLOR_YELLOW}%s${COLOR_RESET}\n" "${1}" "${2}"; }

function __help() { printf "  ${COLOR_GREEN}${SCRIPTNAME} %-30s${COLOR_RESET}\t${COLOR_YELLOW}%s${COLOR_RESET}\n" "${1}" "${2}"; }

function __mainhead(){
      printf "\n"
      printf "${COLOR_WHITE}$*${reset}\n"
}

function __head() {
    printf "\n"
    printf "${COLOR_CYAN}$*${reset}\n"
}

__foot() {
    printf "\n"
    local cols=$(expr $(tput cols) - 10)
    printf "${COLOR_MAGENTA}"'=%.0s'"${COLOR_RESET}" $(seq 1 $cols)
    printf "\n"
    printf "\n"
}

function prompt_for_input() {
    __attn $1
    input_value=""
    while [[ ! -n "$input_value" ]]; do
        if [[ $2 == true ]]; then
            read -s -p "> " input_value 
        else
            read -p "> " input_value 
        fi 
        if [[ ! -n "$input_value" ]]; then
            __err "\nYou can't provide a blank value. Try again!"
        fi
    done
}