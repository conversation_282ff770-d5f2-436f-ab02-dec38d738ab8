function stopOtherContainers() {
    if $DOCKER ps -q | grep -q .; then
      if ! runDockerCompose ps --filter="status=running" -q 2>/dev/null | grep -q .; then
        while true; do
            read -p "Do you want to shut down other containers? (y/n - default yes)" yn
            case $yn in
                [Nn]* )
                    break;;
                * ) stopall; break;;
            esac
        done
      fi
    fi
}

function dockerremoveold () {
  __err "Following containers will be deleted:"
  $DOCKER ps --filter "status=exited" | $GREP 'Exited .* months ago' |  $AWK '{print $12}'
  read -p "Continue (y/n)?" choice
  if [[ "$choice" = "y" || "$choice" = "yes" || "$choice" = "z" ]]; then
      $DOCKER ps --filter "status=exited" | $GREP 'Exited .* months ago' | $AWK '{print $1}' | $XARGS --no-run-if-empty docker rm
  fi
  $DOCKER image prune -a
  $DOCKER volume prune
  # $DOCKER network prune
  # $DOCKER system prune -a   Command to delete everything from device
}

function dockerimageupdate() {
  __info "Updating copex/nginx-php-fpm:dev"
  $DOCKER pull copex/nginx-php-fpm:dev
}

function getContainerDate(){
    if [[ -z "$1"  ||  "$1" = "docker:date" ]]; then
        image="copex/nginx-php-fpm:dev"
    else
        image="$1"
    fi
    date=$($DOCKER inspect -f '{{ .Created }}' $image 2>/dev/null)
    echo $(date --date="$date" +%s)
}

function dockerVolumeLookup(){
    $DOCKER ps -a --filter=volume=$1
}

# Source: https://github.com/gdiepen/docker-convenience-scripts
function dockerVolumeInfo() {
  __msg "List of all volumes:"
  __msg

  #Loop over all the data volumes
  for docker_volume_id in $($DOCKER volume ls -q)
  do
    __msg "(Un)named volume: ${docker_volume_id}"

    #Obtain the size of the data volume by starting a docker container
    #that uses this data volume and determines the size of this data volume
    docker_volume_size=$($DOCKER run --rm -t -v ${docker_volume_id}:/volume_data alpine sh -c "du -hs /volume_data | cut -f1" )

    __msg "    Size: ${docker_volume_size}"

    #Determine the number of stopped and running containers that
    #have a connection to this data volume
    num_related_containers=$($DOCKER ps -a --filter=volume=${docker_volume_id} -q | wc -l)

    #If the number is non-zero, we show the information about the container and the image
    #and otherwise we show the message that are no connected containers
    if (( $num_related_containers > 0 ))
    then
      __msg "    Connected containers:"
      $DOCKER ps -a --filter=volume=${docker_volume_id} --format "{{.Names}} [{{.Image}}] ({{.Status}})" | while read containerDetails
      do
        __msg "        ${containerDetails}"
      done
    else
      __msg "    No connected containers"
    fi

    __msg
  done
}

# Source: https://github.com/gdiepen/docker-convenience-scripts
function dockerVolumeCp(){
  if [ "$1" = "" ]
  then
          __err "Please provide a source volume name"
          exit
  fi

  if [ "$2" = "" ]
  then
          __err "Please provide a destination volume name"
          exit
  fi


  #Check if the source volume name does exist
  $DOCKER volume inspect $1 > /dev/null 2>&1
  if [ "$?" != "0" ]
  then
          __err "The source volume \"$1\" does not exist"
          exit
  fi

  #Now check if the destination volume name does not yet exist
  $DOCKER volume inspect $2 > /dev/null 2>&1

  if [ "$?" = "0" ]
  then
          __err "The destination volume \"$2\" already exists"
          exit
  fi



  __msg "Creating destination volume \"$2\"..."
  $DOCKER volume create --name $2
  __msg "Copying data from source volume \"$1\" to destination volume \"$2\"..."
  $DOCKER run --rm \
             -i \
             -t \
             -v $1:/from \
             -v $2:/to \
             alpine ash -c "cd /from ; cp -av . /to"
}