#!/usr/bin/env bash
[[ ! ${SCRIPT_DIR} ]] && >&2 __err "This script is not intended to be run directly!" && exit 1

function upsearch () {
  slashes=${PWD//[^\/]/}
  directory="$PWD"
  for (( n=${#slashes}; n>0; --n ))
  do
    test -e "$directory/$1" && echo "$directory/$1" && return
    directory="$directory/.."
  done
}

function searchDockerComposeYml(){
    if [[ -f "$rootDir/config/docker-compose.yml" ]]; then
      echo "$(realpath "$rootDir/config/docker-compose.yml")"
    elif [[ -f  "$rootDir/docker-compose.yml" ]]; then
      echo "$(realpath "$rootDir/docker-compose.yml")"
    else
      echo "";
    fi
}

function parseDockerCompose(){
    local docker_compose=$docker_compose_file
    if [[ $docker_compose ]] && [[ -e $docker_compose ]]; then
        if [[ -f "$env" ]]; then
          while IFS= read -r line || [[ -n $line ]]; do
            line=$(echo "$line" | sed 's/[[:space:]]*$//') # Trim trailing whitespace
            if [[ ${line:0:1} != "#" ]] && [[ ! -z $line ]] && [[ $line == *=* ]]; then
              export "$line"
            fi
          done < "$env"
          COMPOSE_PROJECT_NAME=$PROJECT
        fi
        while read line; do export "$line";
            done < <(echo "$(eval "echo \"$(parse_yaml $docker_compose "docker_compose_")\"")" | $SED -e 's/_services_/_/g' -e 's/__/_/g')

        if [[ -f "$docker_compose_path/docker-compose.local.yml" ]]; then
            while read line; do export "$line";
            done < <(echo "$(eval "echo \"$(parse_yaml "$docker_compose_path/docker-compose.local.yml" "docker_compose_")\"")" | $SED -e 's/_services_/_/g' -e 's/__/_/g')
        fi
        if [[ -f "$docker_compose_path/docker-compose.override.yml" ]]; then
            while read line; do export "$line";
            done < <(echo "$(eval "echo \"$(parse_yaml "$docker_compose_path/docker-compose.override.yml" "docker_compose_")\"")" | $SED -e 's/_services_/_/g' -e 's/__/_/g')
        fi
    else
        __err "Couldn't find docker-compose.yml!"
    fi
}

function parse_yaml() {
   local prefix=$2
   local s='[[:space:]]*' w='[a-zA-Z0-9_]*' fs=$(echo @|tr @ '\034')
   sed -ne "s|^\($s\)\($w\)$s:$s\"\(.*\)\"$s\$|\1$fs\2$fs\3|p" \
        -e "s|^\($s\)\($w\)$s:$s\(.*\)$s\$|\1$fs\2$fs\3|p"  $1 |
   awk -F$fs '{
      indent = length($1)/2;
      vname[indent] = $2;
      for (i in vname) {if (i > indent) {delete vname[i]}}
      if (length($3) > 0) {
         vn=""; for (i=0; i<indent; i++) {vn=(vn)(vname[i])("_")}
         printf("%s%s%s=\"%s\"\n", "'$prefix'",vn, $2, $3);
      }
   }'
}


function getContainerIpByType(){
    local CONTAINER_NAME=$(getContainerNameByType "$1")
    local CONTAINER_IP=$($DOCKER inspect --format '{{ .NetworkSettings.IPAddress }}' "$CONTAINER_NAME")
    echo $CONTAINER_IP
    return 0
}

function getContainerPIDByType() {
    local CONTAINER_NAME=$(getContainerNameByType "$1")
    local CONTAINER_PID=$($DOCKER inspect --format '{{ .State.Pid }}' "$CONTAINER_NAME")
    echo $CONTAINER_PID
    return 0
}

#########################################################################
# Get the full container name for the given container type (e.g. "php")
# Arguments:
#  CONTAINER_TYPE
# Returns:
#  The full name of the (first) container with the given type
#########################################################################
function getContainerNameByType() {
    # abort if no type is specified
    local CONTAINER_TYPE="$1"
    if [ -z "$CONTAINER_TYPE" ]; then
        __err "No container type specified. Please specifiy a container type (e.g. app, mysql, ...)."  >&2
        return 1
    fi

    # check if xargs is available
    if [ -z "$XARGS" ]; then
        __err "The tool 'xargs' was not found on your system." >&2
        return 1
    fi

    # check if grep is available
    if [ -z "$GREP" ]; then
        __err "The tool 'grep' was not found on your system." >&2
        return 1
    fi

    # check if sed is available
    if [ -z "$SED" ]; then
        __err "The tool 'sed' was not found on your system." >&2
        return 1
    fi
    if [ "$($DOCKER ps -q -f name=$CONTAINER_TYPE)" ]; then
        local containerName=$($DOCKER ps -q | $XARGS $DOCKER inspect --format '{{.Name}}' | $GREP "$CONTAINER_TYPE" | $SED 's:/::' | $GREP "$CONTAINER_TYPE")
        echo $containerName
        return 0
    else
        __err "Couldn\'t find container ${CONTAINER_TYPE}" >&2
        exit 1
    fi
}

## Detect root dir by .gitignore
function getRootDir(){
    local rootDir=$(upsearch ".gitignore")
    if [[ $rootDir ]]; then
      echo "$(dirname $(realpath $rootDir))"
    else
      echo "";
    fi
}

## function for determining if a value is present in an array
function containsElement () {
  local e match="$1"
  shift
  for e; do [[ "$e" == "$match" ]] && return 0; done
  return 1
}

function list_functions_in_file() {
    declare -F -f -F "$1" | awk '{print $3}'
}

rootDir=$(getRootDir)
if [ -z "$rootDir" ]; then
  __err "Could not detect root dir."
  exit
fi
docker_compose_file=$(searchDockerComposeYml)
if [[ -n "$docker_compose_file" ]]; then
  if [[ $docker_compose_file != *config* && "$NOCOLORS" == "" ]]; then
    while true; do
        read -p "Do you want to move the docker-compose configs into config folder (recommended)? (y|n)" yn
        case $yn in
            [Yy]* )
              cd $rootDir
              docker_compose_file="$(dirname $docker_compose_file)/config/$(basename $docker_compose_file)"
              mv -n docker-compose.yml config/docker-compose.yml 2>/dev/null || true
              mv -n docker-compose.override.yml config/docker-compose.local.yml 2>/dev/null || true
              mv -n docker-compose.local.yml config/docker-compose.local.yml 2>/dev/null || true
              mv -n docker-compose.staging.override.yml config/docker-compose.staging.yml 2>/dev/null || true
              mv -n docker-compose.stage.override.yml config/docker-compose.staging.yml 2>/dev/null || true
              mv -n docker-compose.prod.override.yml config/docker-compose.production.yml 2>/dev/null || true
              mv -n docker-compose.production.override.yml config/docker-compose.production.yml 2>/dev/null || true
              mv -n .env config/local.env 2>/dev/null || true
              mv -n .env.prod config/prod.env 2>/dev/null || true
              mv -n .env.stage config/stage.env 2>/dev/null || true
              mv -n .env.staging config/staging.env 2>/dev/null || true
              sed -i 's|- ./:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.local.yml 2>/dev/null || true
              sed -i 's|- ./:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.override.yml 2>/dev/null || true
              sed -i 's|- ./:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.yml 2>/dev/null || true
              sed -i 's|- .:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.local.yml 2>/dev/null || true
              sed -i 's|- .:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.override.yml 2>/dev/null || true
              sed -i 's|- .:\${DEV_MAGENTO_ROOT}|- ../:\${DEV_MAGENTO_ROOT}|' config/docker-compose.yml 2>/dev/null || true
              sed -i 's|- .:/var/www/htdocs|- ../:/var/www/htdocs|' config/docker-compose.yml 2>/dev/null || true
              sed -i 's|build: config/|build: |' config/docker-compose.local.yml 2>/dev/null || true
              sed -i 's|build: config/|build: |' config/docker-compose.override.yml 2>/dev/null || true
              sed -i 's|build: config/|build: |' config/docker-compose.yml 2>/dev/null || true
              sed -i 's|- ./config/|- ./|' config/docker-compose.local.yml 2>/dev/null || true
              sed -i 's|- ./config/|- ./|' config/docker-compose.override.yml 2>/dev/null || true
              sed -i 's|- ./config/|- ./|' config/docker-compose.yml 2>/dev/null || true
              break;;
            [Nn]* ) break;;
            * ) echo "Please answer [y]es or [n]o.";;
        esac
    done
  fi
  docker_compose_path=$(dirname $docker_compose_file)
  env="$docker_compose_path/local.env"
  if [[ ! -e $env ]]; then
    env="$docker_compose_path/.env"
  fi
fi