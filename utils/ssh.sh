get_server_config() {
  server_hostname=$1
  awk -v hostname="$server_hostname" '
    $1 == "Host" {
      found = 0
      for (i = 2; i <= NF; ++i) {
        if ($i == hostname) {
          found = 1
          break
        }
      }
    }
    found && $1 != "Host" {
      config = config $0 ORS
    }
    END { printf "%s", config }
  ' ~/.ssh/config
}

function transfer_from_server_to_server(){

  if [ $# -eq 4 ]; then
    server_a_hostname=$1
    server_a_path=$2
    server_b_hostname=$3
    server_b_destination=$4
  else
    read -p "Enter Server A hostname: " server_a_hostname
    read -p "Enter the path on Server A: " server_a_path
    read -p "Enter Server B hostname: " server_b_hostname
    read -p "Enter the destination on Server B: " server_b_destination
  fi


  if [ ! -f transfer_key ]; then
    # Generate an SSH key pair
    echo "Generating an SSH key pair for the transfer..."
    ssh-keygen -t ed25519 -f transfer_key -N ""

    # Copy the private key to Server A
    echo "Copying the private key to Server A..."
    scp transfer_key ${server_a_hostname}:~/.ssh/

    # Copy the public key to Server B
    echo "Copying the public key to Server B..."
    scp transfer_key.pub ${server_b_hostname}:~/.ssh/

    # Append the public key to the authorized_keys file on Server B
    echo "Appending the public key to authorized_keys on Server B..."
    ssh ${server_b_hostname} "cat ~/.ssh/transfer_key.pub >> ~/.ssh/authorized_keys"
  fi
  # Copy Server B configuration details from the local machine to Server A
  server_b_config=$(get_server_config "$server_b_hostname")
  echo "Copying Server B SSH configuration details to Server A..."
  ssh ${server_a_hostname} "echo $'Host $server_b_hostname\n$server_b_config' >> ~/.ssh/config"

  # Perform the transfer between Server A and Server B
  echo "Transferring files from Server A to Server B..."
  ssh -i transfer_key ${server_a_hostname} "tar -C ${server_a_path} -czf - . | ssh -o 'StrictHostKeyChecking no' -i ~/.ssh/transfer_key ${server_b_hostname} 'cat > /tmp/transfer.tar.gz'"

  # Create the destination directory on Server B if it doesn't exist
  ssh ${server_b_hostname} "mkdir -p ${server_b_destination}"
  ssh -i transfer_key ${server_b_hostname} "tar xzf /tmp/transfer.tar.gz -C ${server_b_destination}"
  ssh -i transfer_key ${server_b_hostname} "rm /tmp/transfer.tar.gz"



  # Remove the temporary SSH key pair and configuration details from Server A and Server B
  echo "Removing the temporary SSH key pair and configuration details from Server A and Server B..."
  ssh ${server_a_hostname} "rm ~/.ssh/transfer_key && sed -i '/Host $server_b_hostname/,/^$/{/Host $server_b_hostname/{d};{/^$/!d}}' ~/.ssh/config"
  ssh ${server_b_hostname} "rm ~/.ssh/transfer_key.pub && sed -i '\|$(cat transfer_key.pub)|d' ~/.ssh/authorized_keys"

  # Clean up the local temporary key pair
  echo "Cleaning up the local temporary key pair..."
  rm transfer_key transfer_key.pub

  echo "Transfer complete and temporary files removed."
}

function sshTunnel() {
   PARAMS=(${parameters// / })
   if [ "${PARAMS[0]}" ]; then
     host=${PARAMS[0]}
   else
     while true; do
        read -p "Hostname:" host
        if [ "$host" ]; then
          break;
        fi
    done
   fi

  if [ "${PARAMS[1]}" ]; then
    port=${PARAMS[1]}
  else
     while true; do
      read -p "Port:" port
      if [ "$port" ]; then
        break;
      fi
    done
  fi

  if [ "${PARAMS[2]}" ]; then
    proxy=${PARAMS[2]}
  else
     proxy="copex.io"
  fi

  if [ "${PARAMS[3]}" ]; then
    localport=${PARAMS[3]}
  else
     localport="9999"
  fi

  echo "Tunneling $host:$port through $proxy to local port: $localport"

  ssh -L $localport:$host:$port -N $proxy

}

function sshTunnelConfig(){
  if [ -f ${rootDir}/config/tunnel.sh ]; then
    __info "Establishing tunnel"
    ${rootDir}/config/tunnel.sh
  else
    __err "Could not find ${rootDir}/config/tunnel.sh"
  fi
}
