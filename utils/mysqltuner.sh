#usage db user password port memory

function mysqltuner(){
  PARAMS=(${parameters// / })
  local db=localhost
  local port=3306
  local user=root
  local pass=r00t
  local memory=16000
  if [ "${PARAMS[0]}" ]; then
    db="${PARAMS[0]}"
  fi
  if [ "${PARAMS[1]}" ]; then
    user="${PARAMS[1]}"
  fi
    if [ "${PARAMS[2]}" ]; then
    password="${PARAMS[2]}"
  fi
    if [ "${PARAMS[3]}" ]; then
    port="${PARAMS[3]}"
  fi
    if [ "${PARAMS[4]}" ]; then
    memory="${PARAMS[4]}"
  fi
  docker run -it --rm owski/mysqltuner \
  --host "$db" \
  --port "$port" \
  --user "$user" \
  --pass "$password" \
  --forcemem "$memory" \
  --nogood \
  --noinfo
  #https://github.com/owski/docker-mysqltuner
  #https://github.com/good-dba/mariadb-sys/
}