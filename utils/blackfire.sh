function runBlackfire(){

    __err "not supported yet"
    exit
    if [ -z ${BLACK<PERSON>RE_SERVER_ID:-} ]; then
        __err "Please put BLACKFI<PERSON> ENV VARS into ~/.bashrc and reboot your system"
        exit
    fi

    __attn  "BLACKFIRE_CLIENT_ID=${BLACKFIRE_CLIENT_ID}"
    __attn  "BLACKFIRE_CLIENT_TOKEN=${BLACKFIRE_CLIENT_TOKEN}"
    __attn  "BLACKFIRE_SERVER_ID=${BLACKFIRE_SERVER_ID}"
    __attn  "BLACKFIRE_SERVER_TOKEN=${BLACKFIRE_SERVER_TOKEN}"

    $DOCKER run --rm --net=web --net=backend --name="blackfire" -d -e BLACKFIRE_SERVER_ID -e BLACKFIRE_SERVER_TOKEN blackfire/blackfire
}