#!/usr/bin/env bash
[[ ! ${SCRIPT_DIR} ]] && >&2 __err "This script is not intended to be run directly!" && exit 1

## define environment requirements
readonly REQUIRED_DOCKER_COMPOSE="1.18.0"
readonly MAGENTO_HOME_DIR=~/.magento
readonly SSL_DIR="${MAGENTO_HOME_DIR}/ssl"


CURRENT_PATH=$(pwd)
APP="app"
MYSQL="mysql"

# Mandatory Tools
XARGS=$(command -v xargs)
GREP=$(command -v grep)
SED=$(command -v sed)
AWK=$(command -v awk)
DOCKER=$(command -v docker)

if [ -z "$DOCKER" ]; then
    __err "'docker' was not found on your system." >&2
    exit 1
fi

if "$DOCKER" --help | grep -q "compose"; then
  DOCKERCOMPOSE="$DOCKER compose"
else
  DOCKERCOMPOSE=$(command -v docker-compose)
  if [ -z "$DOCKERCOMPOSE" ]; then
      __err "'docker-compose' was not found on your system." >&2
      exit 1
  fi
fi

#
# A control-script for managing the docker-infrastructure components for Magento

LATEST_IMAGE=1587985526

MAGENTO_VERSION=1
DOCKER_USER=www-data